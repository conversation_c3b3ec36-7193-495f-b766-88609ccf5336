#!/bin/bash

# Enhanced BLE Improvements Test Script
# Tests all the implemented improvements for Bluetooth connectivity

APP_PACKAGE="com.bodymount.app"
DEVICE_MAC="F0:9E:9E:0F:E9:25"  # Update with your device MAC
LOG_FILE="enhanced_ble_test_$(date +%Y%m%d_%H%M%S).log"
TEST_DURATION=300  # 5 minutes default

echo "=== Enhanced BLE Improvements Test Script ==="
echo "Testing comprehensive Bluetooth error handling and reconnection improvements"
echo "Log file: $LOG_FILE"
echo ""

# Function to log with timestamp
log_with_timestamp() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to check if app is running
is_app_running() {
    adb shell pidof "$APP_PACKAGE" > /dev/null 2>&1
}

# Function to start the app
start_app() {
    log_with_timestamp "Starting BodyMount-PMS app..."
    adb shell am start -n "$APP_PACKAGE/.MainActivity" > /dev/null 2>&1
    sleep 3
    
    if is_app_running; then
        log_with_timestamp "✅ App started successfully"
        return 0
    else
        log_with_timestamp "❌ Failed to start app"
        return 1
    fi
}

# Function to stop the app
stop_app() {
    log_with_timestamp "Stopping BodyMount-PMS app..."
    adb shell am force-stop "$APP_PACKAGE"
    sleep 2
    
    if ! is_app_running; then
        log_with_timestamp "✅ App stopped successfully"
        return 0
    else
        log_with_timestamp "❌ Failed to stop app"
        return 1
    fi
}

# Function to monitor BLE patterns
monitor_ble_patterns() {
    local duration=$1
    log_with_timestamp "Monitoring BLE patterns for $duration seconds..."
    
    # Monitor for enhanced error handling patterns
    timeout "$duration" adb logcat -s "BodyMountBleManager:*" "BleErrorHandler:*" "ConnectionHealthMonitor:*" "BleReconnectionManager:*" "EnhancedBleManager:*" | while read -r line; do
        echo "$line" >> "$LOG_FILE"
        
        # Check for specific improvement patterns
        if echo "$line" | grep -q "Enhanced error handling"; then
            log_with_timestamp "✅ Enhanced error handling detected"
        fi
        
        if echo "$line" | grep -q "Auto-reconnection"; then
            log_with_timestamp "✅ Auto-reconnection logic detected"
        fi
        
        if echo "$line" | grep -q "Connection Health Report"; then
            log_with_timestamp "✅ Health monitoring active"
        fi
        
        if echo "$line" | grep -q "MTU negotiated successfully"; then
            log_with_timestamp "✅ MTU negotiation successful"
        fi
        
        if echo "$line" | grep -q "Staggered notification"; then
            log_with_timestamp "✅ Staggered notification setup detected"
        fi
    done
}

# Function to test connection stability
test_connection_stability() {
    log_with_timestamp "Testing connection stability..."
    
    # Start monitoring in background
    monitor_ble_patterns 60 &
    local monitor_pid=$!
    
    # Simulate connection/disconnection cycles
    for i in {1..3}; do
        log_with_timestamp "Connection cycle $i/3"
        
        # Force disconnect by disabling/enabling Bluetooth
        log_with_timestamp "Simulating disconnection..."
        adb shell svc bluetooth disable
        sleep 5
        adb shell svc bluetooth enable
        sleep 10
        
        # Check for reconnection attempts
        if adb logcat -d | grep -q "Auto-reconnection"; then
            log_with_timestamp "✅ Auto-reconnection triggered"
        else
            log_with_timestamp "⚠️ Auto-reconnection not detected"
        fi
    done
    
    # Stop monitoring
    kill $monitor_pid 2>/dev/null
}

# Function to test error handling
test_error_handling() {
    log_with_timestamp "Testing enhanced error handling..."
    
    # Look for error handling patterns in recent logs
    local error_patterns=(
        "BleErrorHandler.*Error category"
        "BleErrorHandler.*Recovery.*RETRY_WITH_BACKOFF"
        "BleErrorHandler.*Error is retryable"
        "ConnectionHealthMonitor.*Operation.*failed"
        "Retrying.*in.*ms"
    )
    
    for pattern in "${error_patterns[@]}"; do
        if adb logcat -d | grep -E "$pattern" > /dev/null; then
            log_with_timestamp "✅ Error handling pattern detected: $pattern"
        else
            log_with_timestamp "⚠️ Error handling pattern not found: $pattern"
        fi
    done
}

# Function to test UI responsiveness
test_ui_responsiveness() {
    log_with_timestamp "Testing UI responsiveness during disconnections..."
    
    # Monitor for UI freezing indicators
    local ui_patterns=(
        "MainActivityUIHelper.*updating UI"
        "EnhancedDialogManager.*dialog"
        "UI.*main thread"
        "Dispatchers.Main"
    )
    
    for pattern in "${ui_patterns[@]}"; do
        if adb logcat -d | grep -E "$pattern" > /dev/null; then
            log_with_timestamp "✅ UI responsiveness pattern detected: $pattern"
        else
            log_with_timestamp "⚠️ UI pattern not found: $pattern"
        fi
    done
}

# Function to analyze health metrics
analyze_health_metrics() {
    log_with_timestamp "Analyzing connection health metrics..."
    
    # Extract health metrics from logs
    local health_report=$(adb logcat -d | grep "Connection Health Report" -A 10 | tail -1)
    
    if [ -n "$health_report" ]; then
        log_with_timestamp "✅ Health metrics available"
        echo "$health_report" >> "$LOG_FILE"
        
        # Check for health status
        if echo "$health_report" | grep -q "HEALTHY"; then
            log_with_timestamp "✅ Connection health status: HEALTHY"
        elif echo "$health_report" | grep -q "UNHEALTHY"; then
            log_with_timestamp "⚠️ Connection health status: UNHEALTHY"
        fi
    else
        log_with_timestamp "⚠️ No health metrics found"
    fi
}

# Function to run comprehensive test
run_comprehensive_test() {
    local duration=${1:-$TEST_DURATION}
    
    log_with_timestamp "Starting comprehensive BLE improvements test (${duration}s)"
    log_with_timestamp "Target device: $DEVICE_MAC"
    
    # Clear logcat
    adb logcat -c
    
    # Start app
    if ! start_app; then
        log_with_timestamp "❌ Cannot start app, aborting test"
        return 1
    fi
    
    # Wait for app initialization
    sleep 5
    
    # Test phases
    log_with_timestamp "Phase 1: Testing error handling improvements"
    test_error_handling
    
    log_with_timestamp "Phase 2: Testing connection stability"
    test_connection_stability
    
    log_with_timestamp "Phase 3: Testing UI responsiveness"
    test_ui_responsiveness
    
    log_with_timestamp "Phase 4: Analyzing health metrics"
    analyze_health_metrics
    
    # Generate summary report
    generate_test_report
    
    log_with_timestamp "Comprehensive test completed"
}

# Function to generate test report
generate_test_report() {
    local report_file="ble_improvements_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# Enhanced BLE Improvements Test Report

**Date:** $(date)
**Duration:** $TEST_DURATION seconds
**Device:** $DEVICE_MAC

## Test Results Summary

### ✅ Implemented Improvements Detected:
$(grep "✅" "$LOG_FILE" | sed 's/.*] /- /')

### ⚠️ Areas for Attention:
$(grep "⚠️" "$LOG_FILE" | sed 's/.*] /- /')

### ❌ Issues Found:
$(grep "❌" "$LOG_FILE" | sed 's/.*] /- /')

## Detailed Log Analysis

### Error Handling Patterns:
$(adb logcat -d | grep -E "BleErrorHandler|ConnectionHealthMonitor" | tail -20)

### Reconnection Patterns:
$(adb logcat -d | grep -E "BleReconnectionManager|Auto-reconnection" | tail -10)

### Health Metrics:
$(adb logcat -d | grep "Connection Health Report" -A 5 | tail -10)

## Recommendations

Based on the test results:
1. Monitor error rates and implement additional retry strategies if needed
2. Verify auto-reconnection triggers correctly for all disconnection scenarios
3. Ensure UI remains responsive during all BLE operations
4. Consider adjusting health monitoring thresholds based on device behavior

EOF

    log_with_timestamp "Test report generated: $report_file"
}

# Main execution
case "${1:-test}" in
    "start")
        start_app
        ;;
    "stop")
        stop_app
        ;;
    "monitor")
        duration=${2:-30}
        monitor_ble_patterns "$duration"
        ;;
    "test")
        duration=${2:-$TEST_DURATION}
        run_comprehensive_test "$duration"
        ;;
    "stability")
        test_connection_stability
        ;;
    "errors")
        test_error_handling
        ;;
    "ui")
        test_ui_responsiveness
        ;;
    "health")
        analyze_health_metrics
        ;;
    "report")
        generate_test_report
        ;;
    *)
        echo "Enhanced BLE Improvements Test Script"
        echo ""
        echo "Usage: $0 <command> [options]"
        echo ""
        echo "Commands:"
        echo "  start                    - Start the BodyMount-PMS app"
        echo "  stop                     - Stop the BodyMount-PMS app"
        echo "  monitor [duration]       - Monitor BLE patterns for specified seconds"
        echo "  test [duration]          - Run comprehensive test suite"
        echo "  stability                - Test connection stability"
        echo "  errors                   - Test error handling improvements"
        echo "  ui                       - Test UI responsiveness"
        echo "  health                   - Analyze health metrics"
        echo "  report                   - Generate test report"
        echo ""
        echo "Examples:"
        echo "  $0 test 300             - Run 5-minute comprehensive test"
        echo "  $0 monitor 60           - Monitor for 60 seconds"
        echo "  $0 stability            - Test connection stability only"
        ;;
esac
